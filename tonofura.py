"""
Fake requests to fetch **adult** story resources json from game server.

How to:
1. Edit info in ###Config part to yours.
2. Fetch "getMasterData.json". (Read comments under main())
3. Run.
4. Collect jsons and profit.
"""

import time
import requests
import hmac
import hashlib
import base64
import urllib.parse
import math
import random
import json
import os

###CONFIG
"""
    Press F12 and refresh the game page. Then wait until loading bar ui appears.
    1. How to get "user_id", "game_server_secret" and "game_server_token":
        Search "getDmmAccessToken" under Network tab.
    2. How to get "user_id", "consumer_key" and "consumer_secret":
        Search "json" under Network tab, and find a json starts with:
            {"__type__":"cc.JsonAsset","_name":"environment"...
        Key & Secret exist in this "environment.jsonasset".
"""
user_id = "**********************"  # getDmmAccessToken.UserId
consumer_key = (
    "********************************"  # environment.jsonasset auth.consumerKey
)
consumer_secret = "****************************************************************************************************************************************************************************"  # environment.jsonasset auth.consumerSecret

game_server_secret = "**********************************************************************"  # getDmmAccessToken.secret
game_server_token = "*********************"  # getDmmAccessToken.token
###CONFIG

addStoryUrl = "https://tonofura-web-r.deepone-online.com/deep-one/api/story/addStory"
getResourceUrl = (
    "https://tonofura-web-r.deepone-online.com/deep-one/api/story/getResource"
)


def _encode(t):
    return (
        urllib.parse.quote_plus(t)
        .replace("(", "%28")
        .replace(")", "%29")
        .replace("$", "%24")
        .replace("!", "%21")
        .replace("*", "%2A")
        .replace("'", "%27")
        .replace("%7E", "~")
    )


def getTimeStamp():
    ts = time.time()
    return int(ts)


def getNonce():
    return math.floor(random.random() * 9007199254740991)


def HmacSHA256(message):
    secret = "%s&%s" % (consumer_secret, game_server_secret)
    _message = bytes(message, "utf-8")
    _secret = bytes(secret, "utf-8")
    signature = base64.b64encode(
        hmac.new(_secret, _message, digestmod=hashlib.sha256).digest()
    )
    return signature


def generateAuthorization(requestUrl):
    authorization = {}
    authorization["OAuth realm"] = "Users"
    authorization["oauth_token"] = game_server_token
    authorization["xoauth_requestor_id"] = user_id
    authorization["oauth_consumer_key"] = consumer_key
    authorization["oauth_signature_method"] = "HMAC-SHA256"
    authorization["oauth_nonce"] = getNonce()
    authorization["oauth_timestamp"] = getTimeStamp()
    r = (
        "oauth_consumer_key=%s&oauth_nonce=%s&oauth_signature_method=%s&oauth_timestamp=%s&oauth_token=%s&xoauth_requestor_id=%s"
        % (
            authorization["oauth_consumer_key"],
            authorization["oauth_nonce"],
            authorization["oauth_signature_method"],
            authorization["oauth_timestamp"],
            authorization["oauth_token"],
            authorization["xoauth_requestor_id"],
        )
    )
    message = "POST&%s&%s" % (_encode(requestUrl), _encode(r))
    authorization["oauth_signature"] = HmacSHA256(message).decode("utf-8")
    return (
        'OAuth realm="%s" oauth_token="%s" xoauth_requestor_id="%s" oauth_consumer_key="%s" oauth_signature_method="%s" oauth_nonce="%s" oauth_timestamp="%s" oauth_signature="%s"'
        % (
            authorization["OAuth realm"],
            authorization["oauth_token"],
            authorization["xoauth_requestor_id"],
            authorization["oauth_consumer_key"],
            authorization["oauth_signature_method"],
            authorization["oauth_nonce"],
            authorization["oauth_timestamp"],
            authorization["oauth_signature"],
        )
    )


def generateHeader(requestUrl):
    header = {}
    header["accept"] = "application/json;charset=UTF-8"
    header["accept-language"] = "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7,en-US;q=0.6"
    header["content-type"] = "application/json;charset=UTF-8"
    header["authorization"] = generateAuthorization(requestUrl)
    header["x-deep-one-app-version"] = "{}"
    return header


def postRequest(requestUrl, story_id):
    addData = (
        '{"storyId":"%s"}' % story_id
        if "addStory" in requestUrl
        else '{"storyIds":"%s","adult":1}' % story_id
    )
    r = requests.post(requestUrl, headers=generateHeader(requestUrl), data=addData)
    print(f"{story_id}: {r}")
    # print(r.text)
    if r.status_code == 200 and "getResource" in requestUrl:
        os.makedirs("json", exist_ok=True)
        f = open(f"json/{story_id}.json", "w")
        f.write(r.text)
        f.close()


def getDownloadJson(story_id):
    """
    Server will check requested story_id. If story_id does not exist in user story data, server will return
        {"status":10053,"err":"not available user story"}.
    addStory api can add story_id into user story data.
    Call addStory before getResource will address this issue.
    """
    postRequest(addStoryUrl, story_id)
    postRequest(getResourceUrl, story_id)


def parseStoryJsonDemoCode(path):
    """
    Demo code snippet to process story json.
    """
    url = "https://tonofura-r-cdn-resource.deepone-online.com/deep_one/download_adv/"
    data = json.load(open(path))
    data = data["resource"]
    for i in data:
        path = urllib.parse.urlparse(i["fileName"]).path
        filename, ext = os.path.splitext(path)
        filename = (
            filename.replace("character/", "")
            .replace("/", "_")
            .replace("download_", "download/")
        )
        if ext in [".mp4", ".jpg"]:  # If you don't want mp3s
            print(f'{filename}{ext},{url}{i["path"]}/{i["md5"]}{ext}')


def main():
    """
    Search "getMasterData" in developer tools under network tab.
    Find a msgpacked json with character master data.
    """
    data = json.load(open("getMasterData.json", "r", encoding="utf8"))
    data = data["CharacterContents"]
    for d in data:
        if d["contentType"] == 3:  # adult
            if not os.path.exists(f"json/{d['storyId']}.json"):
                getDownloadJson(d["storyId"])
    # getDownloadJson('19190404')
    # postRequest(getResourceUrl, '19190404')


if __name__ == "__main__":
    main()
